// Calculation utilities for project estimation - Complete Excel Logic

export const WORK_TYPES = {
  UX: {
    simple: { hours: 9, factor: 1 },
    medium: { hours: 18, factor: 1 },
    complex: { hours: 27, factor: 1 }
  },
  FRONTEND: {
    simple: { hours: 9, factor: 1 },
    medium: { hours: 27, factor: 1 },
    complex: { hours: 45, factor: 1 }
  },
  QA: {
    simple: { hours: 9, factor: 1 },
    medium: { hours: 27, factor: 1 },
    complex: { hours: 45, factor: 1 }
  },
  API: {
    simple: { hours: 18, factor: 1 },
    medium: { hours: 45, factor: 1 },
    complex: { hours: 72, factor: 1 }
  },
  POWERAPPS: {
    simple: { hours: 9, factor: 1 },
    medium: { hours: 27, factor: 1 },
    complex: { hours: 45, factor: 1 }
  },
  ANDROID: {
    simple: { hours: 18, factor: 1 },
    medium: { hours: 45, factor: 1 },
    complex: { hours: 72, factor: 1 }
  },
  IOS: {
    simple: { hours: 18, factor: 1 },
    medium: { hours: 45, factor: 1 },
    complex: { hours: 72, factor: 1 }
  },
  HYBRID: {
    simple: { hours: 18, factor: 1 },
    medium: { hours: 45, factor: 1 },
    complex: { hours: 72, factor: 1 }
  }
};

export const MULTIPLIERS = {
  infrastructureSupport: 0, // Environment setup/CICD
  feIntegration: 0, // FE Integration
  frontEndArchitecture: 0, // Front-End: Initial Architecture/Framework
  apiIntegration: 0, // API Integration
  qaAdditionalSupport: 0, // QA Additional Support
  managerEffort: 0, // Manager Effort
  contingencyBuffer: 0.05, // Contingency Buffer (5%)
  projectTypeAdjustment: 0 // Project Type Adjustment (B)
};

// Hours per week for calculations
export const HOURS_PER_WEEK = 45;

// Pricing rate (can be configured)
export const PRICING_RATE = 45; // Default rate per hour

export const calculateWorkTypeTotal = (workType, units) => {
  const { simple = 0, medium = 0, complex = 0 } = units;
  const config = WORK_TYPES[workType];
  
  if (!config) return 0;
  
  const simpleTotal = simple * config.simple.hours;
  const mediumTotal = medium * config.medium.hours;
  const complexTotal = complex * config.complex.hours;
  
  return simpleTotal + mediumTotal + complexTotal;
};

export const calculateTotalDevelopmentHours = (analysisData) => {
  let total = 0;
  
  Object.keys(WORK_TYPES).forEach(workType => {
    if (analysisData[workType]) {
      total += calculateWorkTypeTotal(workType, analysisData[workType]);
    }
  });
  
  return total;
};

export const calculateProjectSummary = (analysisData) => {
  // Calculate individual work type totals (matching Excel Aggregation section)
  const uxTotal = calculateWorkTypeTotal('UX', analysisData.UX || {});
  const frontendTotal = calculateWorkTypeTotal('FRONTEND', analysisData.FRONTEND || {});
  const qaTotal = calculateWorkTypeTotal('QA', analysisData.QA || {});
  const apiTotal = calculateWorkTypeTotal('API', analysisData.API || {});
  const powerAppsTotal = calculateWorkTypeTotal('POWERAPPS', analysisData.POWERAPPS || {});
  const androidTotal = calculateWorkTypeTotal('ANDROID', analysisData.ANDROID || {});
  const iosTotal = calculateWorkTypeTotal('IOS', analysisData.IOS || {});
  const hybridTotal = calculateWorkTypeTotal('HYBRID', analysisData.HYBRID || {});

  // Total Development Hours (sum of all work types)
  const baseDevelopmentHours = uxTotal + frontendTotal + qaTotal + apiTotal +
                              powerAppsTotal + androidTotal + iosTotal + hybridTotal;

  // Calculate additional efforts based on Excel formulas
  const infrastructureHours = baseDevelopmentHours * MULTIPLIERS.infrastructureSupport;
  const feIntegrationHours = frontendTotal * MULTIPLIERS.feIntegration; // Based on FE total
  const frontEndArchitectureHours = frontendTotal * MULTIPLIERS.frontEndArchitecture; // Based on FE total
  const apiIntegrationHours = apiTotal * MULTIPLIERS.apiIntegration; // Based on API total
  const qaAdditionalHours = baseDevelopmentHours * MULTIPLIERS.qaAdditionalSupport;
  const managerEffortHours = baseDevelopmentHours * MULTIPLIERS.managerEffort;
  const contingencyHours = baseDevelopmentHours * MULTIPLIERS.contingencyBuffer;

  // Total SDLC (A) = Development + all additional efforts
  const totalSDLCHours = baseDevelopmentHours + infrastructureHours + feIntegrationHours +
                        frontEndArchitectureHours + apiIntegrationHours + qaAdditionalHours +
                        managerEffortHours + contingencyHours;

  // Project Type Adjustment (B)
  const projectTypeAdjustmentHours = totalSDLCHours * MULTIPLIERS.projectTypeAdjustment;

  // Grand Total (A + B)
  const grandTotalHours = totalSDLCHours + projectTypeAdjustmentHours;
  const grandTotalWeeks = grandTotalHours / HOURS_PER_WEEK;

  // Approximate pricing
  const approximatePricing = grandTotalHours * PRICING_RATE;

  return {
    // Individual work type totals
    uxTotal,
    frontendTotal,
    qaTotal,
    apiTotal,
    powerAppsTotal,
    androidTotal,
    iosTotal,
    hybridTotal,

    // Development totals
    baseDevelopmentHours,

    // Additional effort breakdown
    infrastructureHours,
    feIntegrationHours,
    frontEndArchitectureHours,
    apiIntegrationHours,
    qaAdditionalHours,
    managerEffortHours,
    contingencyHours,

    // Final totals
    totalSDLCHours,
    projectTypeAdjustmentHours,
    grandTotalHours,
    grandTotalWeeks,
    approximatePricing,

    // Work type breakdown with weeks
    workTypeBreakdown: {
      UX: { hours: uxTotal, weeks: uxTotal / HOURS_PER_WEEK },
      FRONTEND: { hours: frontendTotal, weeks: frontendTotal / HOURS_PER_WEEK },
      QA: { hours: qaTotal, weeks: qaTotal / HOURS_PER_WEEK },
      API: { hours: apiTotal, weeks: apiTotal / HOURS_PER_WEEK },
      POWERAPPS: { hours: powerAppsTotal, weeks: powerAppsTotal / HOURS_PER_WEEK },
      ANDROID: { hours: androidTotal, weeks: androidTotal / HOURS_PER_WEEK },
      IOS: { hours: iosTotal, weeks: iosTotal / HOURS_PER_WEEK },
      HYBRID: { hours: hybridTotal, weeks: hybridTotal / HOURS_PER_WEEK }
    }
  };
};

export const getDefaultAnalysisData = () => {
  const defaultData = {};
  
  Object.keys(WORK_TYPES).forEach(workType => {
    defaultData[workType] = {
      simple: 0,
      medium: 0,
      complex: 0
    };
  });
  
  return defaultData;
};
