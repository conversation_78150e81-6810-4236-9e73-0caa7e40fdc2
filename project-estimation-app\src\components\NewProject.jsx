import React, { useState } from 'react';
import { useProjects } from '../context/ProjectContext';
import { getDefaultAnalysisData, PROJECT_COMPONENTS, getTasksForComponents } from '../utils/calculations';

const NewProject = ({ onBack, onProjectCreated }) => {
  const { addProject } = useProjects();
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    client: '',
    status: 'Draft'
  });
  const [selectedComponents, setSelectedComponents] = useState([]);

  const handleSubmit = (e) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      alert('Project name is required');
      return;
    }

    if (selectedComponents.length === 0) {
      alert('Please select at least one component');
      return;
    }

    const newProject = {
      ...formData,
      selectedComponents,
      tasks: getTasksForComponents(selectedComponents),
      analysisData: getDefaultAnalysisData(selectedComponents),
      summary: null
    };

    const createdProject = addProject(newProject);
    onProjectCreated(createdProject);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleComponentToggle = (componentKey) => {
    setSelectedComponents(prev => {
      if (prev.includes(componentKey)) {
        return prev.filter(key => key !== componentKey);
      } else {
        return [...prev, componentKey];
      }
    });
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="mb-6">
        <button
          onClick={onBack}
          className="text-blue-600 hover:text-blue-800 font-medium"
        >
          ← Back to Projects
        </button>
      </div>

      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Create New Project Estimation</h3>
        </div>

        <form onSubmit={handleSubmit} className="px-6 py-4 space-y-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">
              Project Name *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm px-3 py-2 border"
              placeholder="Enter project name"
            />
          </div>

          <div>
            <label htmlFor="client" className="block text-sm font-medium text-gray-700">
              Client
            </label>
            <input
              type="text"
              id="client"
              name="client"
              value={formData.client}
              onChange={handleChange}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm px-3 py-2 border"
              placeholder="Enter client name"
            />
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700">
              Description
            </label>
            <textarea
              id="description"
              name="description"
              rows={4}
              value={formData.description}
              onChange={handleChange}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm px-3 py-2 border"
              placeholder="Enter project description"
            />
          </div>

          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700">
              Status
            </label>
            <select
              id="status"
              name="status"
              value={formData.status}
              onChange={handleChange}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm px-3 py-2 border"
            >
              <option value="Draft">Draft</option>
              <option value="In Progress">In Progress</option>
              <option value="Review">Review</option>
              <option value="Approved">Approved</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Project Components *
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {Object.entries(PROJECT_COMPONENTS).map(([key, component]) => (
                <div
                  key={key}
                  className={`relative rounded-lg border p-4 cursor-pointer transition-all ${
                    selectedComponents.includes(key)
                      ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-500'
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                  onClick={() => handleComponentToggle(key)}
                >
                  <div className="flex items-start">
                    <div className="flex items-center h-5">
                      <input
                        type="checkbox"
                        checked={selectedComponents.includes(key)}
                        onChange={() => handleComponentToggle(key)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </div>
                    <div className="ml-3">
                      <label className="text-sm font-medium text-gray-900 cursor-pointer">
                        {component.label}
                      </label>
                      <p className="text-xs text-gray-500 mt-1">
                        {component.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            {selectedComponents.length === 0 && (
              <p className="mt-2 text-sm text-red-600">Please select at least one component</p>
            )}
          </div>

          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onBack}
              className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Create Project
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default NewProject;
