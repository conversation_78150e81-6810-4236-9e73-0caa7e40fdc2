import React, { useState, useEffect } from 'react';
import { useProjects } from '../context/ProjectContext';
import { calculateProjectSummary } from '../utils/calculations';

const AnalysisTable = ({ project, onBack }) => {
  const { updateProject } = useProjects();
  const [analysisData, setAnalysisData] = useState(project.analysisData || {});
  const [summary, setSummary] = useState(null);

  // Sample tasks data - you can modify this based on your needs
  const tasks = [
    { category: 'Authentication & Dashboard', subtasks: ['Sign In', 'Home Page'] },
    { category: 'Vehicle Registration', subtasks: ['Vehicle Registration List', 'New Vehicle Regs', 'Edit Vehicle Registration', 'Vehicle Details'] },
    { category: 'Vehicle Departure Management', subtasks: ['Vehicle Departure List', 'Vehicle Departure Details', 'Vehicle Pending Departure Form', 'Vehicle Pending Departure Approval'] },
    { category: 'Entry Logs', subtasks: ['Entry Log List', 'Create/EDIT', 'Entry Log View'] },
    { category: 'Material Management', subtasks: ['Material Management', 'Edit', 'Delete'] },
    { category: 'Vendor Management', subtasks: ['Vendor Management List', 'Edit', 'Delete'] },
    { category: 'Product Management', subtasks: ['Product List', 'Add New Product'] },
    { category: 'User Management', subtasks: ['User List', 'Add New User'] },
    { category: 'Reporting', subtasks: ['Reports'] },
    { category: 'Localization', subtasks: ['Localization'] },
    { category: 'QA', subtasks: ['QA Testing'] }
  ];

  useEffect(() => {
    const newSummary = calculateProjectSummary(analysisData);
    setSummary(newSummary);
    
    // Update project with new analysis data and summary
    updateProject(project.id, {
      analysisData,
      summary: newSummary
    });
  }, [analysisData, project.id, updateProject]);

  const handleCellChange = (taskIndex, subtaskIndex, workType, complexity, value) => {
    const numValue = parseInt(value) || 0;
    
    setAnalysisData(prev => {
      const newData = { ...prev };
      
      // Initialize work type if it doesn't exist
      if (!newData[workType]) {
        newData[workType] = { simple: 0, medium: 0, complex: 0 };
      }
      
      // Create a unique key for this cell
      const cellKey = `${taskIndex}-${subtaskIndex}`;
      if (!newData[workType][complexity + '_breakdown']) {
        newData[workType][complexity + '_breakdown'] = {};
      }
      
      // Store the old value to calculate difference
      const oldValue = newData[workType][complexity + '_breakdown'][cellKey] || 0;
      const difference = numValue - oldValue;
      
      // Update the breakdown
      newData[workType][complexity + '_breakdown'][cellKey] = numValue;
      
      // Update the total for this complexity
      newData[workType][complexity] = (newData[workType][complexity] || 0) + difference;
      
      return newData;
    });
  };

  const getCellValue = (taskIndex, subtaskIndex, workType, complexity) => {
    if (!analysisData[workType] || !analysisData[workType][complexity + '_breakdown']) {
      return '';
    }
    const cellKey = `${taskIndex}-${subtaskIndex}`;
    return analysisData[workType][complexity + '_breakdown'][cellKey] || '';
  };

  const getWorkTypeTotal = (workType, complexity) => {
    return analysisData[workType]?.[complexity] || 0;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <button
          onClick={onBack}
          className="text-blue-600 hover:text-blue-800 font-medium"
        >
          ← Back to Projects
        </button>
        <h2 className="text-2xl font-bold text-gray-900">{project.name} - Analysis</h2>
        <div></div>
      </div>

      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Effort
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Subtasks
                </th>
                <th colSpan="3" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-l border-gray-200">
                  UX
                </th>
                <th colSpan="3" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-l border-gray-200">
                  FE
                </th>
                <th colSpan="3" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-l border-gray-200">
                  QA
                </th>
                <th colSpan="3" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-l border-gray-200">
                  API
                </th>
                <th colSpan="3" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-l border-gray-200">
                  PowerApps
                </th>
                <th colSpan="3" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-l border-gray-200">
                  Android
                </th>
                <th colSpan="3" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-l border-gray-200">
                  iOS
                </th>
                <th colSpan="3" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-l border-gray-200">
                  Hybrid App
                </th>
              </tr>
              <tr className="bg-gray-100">
                <th className="px-6 py-2"></th>
                <th className="px-6 py-2 text-xs font-medium text-gray-500">Subtasks</th>
                <th className="px-3 py-2 text-xs font-medium text-gray-500 border-l border-gray-200">Simple</th>
                <th className="px-3 py-2 text-xs font-medium text-gray-500">Medium</th>
                <th className="px-3 py-2 text-xs font-medium text-gray-500">Complex</th>
                <th className="px-3 py-2 text-xs font-medium text-gray-500 border-l border-gray-200">Simple</th>
                <th className="px-3 py-2 text-xs font-medium text-gray-500">Medium</th>
                <th className="px-3 py-2 text-xs font-medium text-gray-500">Complex</th>
                <th className="px-3 py-2 text-xs font-medium text-gray-500 border-l border-gray-200">Simple</th>
                <th className="px-3 py-2 text-xs font-medium text-gray-500">Medium</th>
                <th className="px-3 py-2 text-xs font-medium text-gray-500">Complex</th>
                <th className="px-3 py-2 text-xs font-medium text-gray-500 border-l border-gray-200">Simple</th>
                <th className="px-3 py-2 text-xs font-medium text-gray-500">Medium</th>
                <th className="px-3 py-2 text-xs font-medium text-gray-500">Complex</th>
                <th className="px-3 py-2 text-xs font-medium text-gray-500 border-l border-gray-200">Simple</th>
                <th className="px-3 py-2 text-xs font-medium text-gray-500">Medium</th>
                <th className="px-3 py-2 text-xs font-medium text-gray-500">Complex</th>
                <th className="px-3 py-2 text-xs font-medium text-gray-500 border-l border-gray-200">Simple</th>
                <th className="px-3 py-2 text-xs font-medium text-gray-500">Medium</th>
                <th className="px-3 py-2 text-xs font-medium text-gray-500">Complex</th>
                <th className="px-3 py-2 text-xs font-medium text-gray-500 border-l border-gray-200">Simple</th>
                <th className="px-3 py-2 text-xs font-medium text-gray-500">Medium</th>
                <th className="px-3 py-2 text-xs font-medium text-gray-500">Complex</th>
                <th className="px-3 py-2 text-xs font-medium text-gray-500 border-l border-gray-200">Simple</th>
                <th className="px-3 py-2 text-xs font-medium text-gray-500">Medium</th>
                <th className="px-3 py-2 text-xs font-medium text-gray-500">Complex</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {tasks.map((task, taskIndex) => (
                <React.Fragment key={taskIndex}>
                  <tr className="bg-blue-50">
                    <td colSpan="26" className="px-6 py-3 text-sm font-medium text-gray-900">
                      {task.category}
                    </td>
                  </tr>
                  {task.subtasks.map((subtask, subtaskIndex) => (
                    <tr key={`${taskIndex}-${subtaskIndex}`} className="hover:bg-gray-50">
                      <td className="px-6 py-2"></td>
                      <td className="px-6 py-2 text-sm text-gray-900">{subtask}</td>
                      
                      {/* UX columns */}
                      <td className="px-3 py-2 border-l border-gray-200">
                        <input
                          type="number"
                          min="0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          value={getCellValue(taskIndex, subtaskIndex, 'UX', 'simple')}
                          onChange={(e) => handleCellChange(taskIndex, subtaskIndex, 'UX', 'simple', e.target.value)}
                        />
                      </td>
                      <td className="px-3 py-2">
                        <input
                          type="number"
                          min="0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          value={getCellValue(taskIndex, subtaskIndex, 'UX', 'medium')}
                          onChange={(e) => handleCellChange(taskIndex, subtaskIndex, 'UX', 'medium', e.target.value)}
                        />
                      </td>
                      <td className="px-3 py-2">
                        <input
                          type="number"
                          min="0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          value={getCellValue(taskIndex, subtaskIndex, 'UX', 'complex')}
                          onChange={(e) => handleCellChange(taskIndex, subtaskIndex, 'UX', 'complex', e.target.value)}
                        />
                      </td>
                      
                      {/* FRONTEND columns */}
                      <td className="px-3 py-2 border-l border-gray-200">
                        <input
                          type="number"
                          min="0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          value={getCellValue(taskIndex, subtaskIndex, 'FRONTEND', 'simple')}
                          onChange={(e) => handleCellChange(taskIndex, subtaskIndex, 'FRONTEND', 'simple', e.target.value)}
                        />
                      </td>
                      <td className="px-3 py-2">
                        <input
                          type="number"
                          min="0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          value={getCellValue(taskIndex, subtaskIndex, 'FRONTEND', 'medium')}
                          onChange={(e) => handleCellChange(taskIndex, subtaskIndex, 'FRONTEND', 'medium', e.target.value)}
                        />
                      </td>
                      <td className="px-3 py-2">
                        <input
                          type="number"
                          min="0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          value={getCellValue(taskIndex, subtaskIndex, 'FRONTEND', 'complex')}
                          onChange={(e) => handleCellChange(taskIndex, subtaskIndex, 'FRONTEND', 'complex', e.target.value)}
                        />
                      </td>
                      
                      {/* QA columns */}
                      <td className="px-3 py-2 border-l border-gray-200">
                        <input
                          type="number"
                          min="0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          value={getCellValue(taskIndex, subtaskIndex, 'QA', 'simple')}
                          onChange={(e) => handleCellChange(taskIndex, subtaskIndex, 'QA', 'simple', e.target.value)}
                        />
                      </td>
                      <td className="px-3 py-2">
                        <input
                          type="number"
                          min="0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          value={getCellValue(taskIndex, subtaskIndex, 'QA', 'medium')}
                          onChange={(e) => handleCellChange(taskIndex, subtaskIndex, 'QA', 'medium', e.target.value)}
                        />
                      </td>
                      <td className="px-3 py-2">
                        <input
                          type="number"
                          min="0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          value={getCellValue(taskIndex, subtaskIndex, 'QA', 'complex')}
                          onChange={(e) => handleCellChange(taskIndex, subtaskIndex, 'QA', 'complex', e.target.value)}
                        />
                      </td>
                      
                      {/* API columns */}
                      <td className="px-3 py-2 border-l border-gray-200">
                        <input
                          type="number"
                          min="0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          value={getCellValue(taskIndex, subtaskIndex, 'API', 'simple')}
                          onChange={(e) => handleCellChange(taskIndex, subtaskIndex, 'API', 'simple', e.target.value)}
                        />
                      </td>
                      <td className="px-3 py-2">
                        <input
                          type="number"
                          min="0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          value={getCellValue(taskIndex, subtaskIndex, 'API', 'medium')}
                          onChange={(e) => handleCellChange(taskIndex, subtaskIndex, 'API', 'medium', e.target.value)}
                        />
                      </td>
                      <td className="px-3 py-2">
                        <input
                          type="number"
                          min="0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          value={getCellValue(taskIndex, subtaskIndex, 'API', 'complex')}
                          onChange={(e) => handleCellChange(taskIndex, subtaskIndex, 'API', 'complex', e.target.value)}
                        />
                      </td>

                      {/* PowerApps columns */}
                      <td className="px-3 py-2 border-l border-gray-200">
                        <input
                          type="number"
                          min="0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          value={getCellValue(taskIndex, subtaskIndex, 'POWERAPPS', 'simple')}
                          onChange={(e) => handleCellChange(taskIndex, subtaskIndex, 'POWERAPPS', 'simple', e.target.value)}
                        />
                      </td>
                      <td className="px-3 py-2">
                        <input
                          type="number"
                          min="0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          value={getCellValue(taskIndex, subtaskIndex, 'POWERAPPS', 'medium')}
                          onChange={(e) => handleCellChange(taskIndex, subtaskIndex, 'POWERAPPS', 'medium', e.target.value)}
                        />
                      </td>
                      <td className="px-3 py-2">
                        <input
                          type="number"
                          min="0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          value={getCellValue(taskIndex, subtaskIndex, 'POWERAPPS', 'complex')}
                          onChange={(e) => handleCellChange(taskIndex, subtaskIndex, 'POWERAPPS', 'complex', e.target.value)}
                        />
                      </td>

                      {/* Android columns */}
                      <td className="px-3 py-2 border-l border-gray-200">
                        <input
                          type="number"
                          min="0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          value={getCellValue(taskIndex, subtaskIndex, 'ANDROID', 'simple')}
                          onChange={(e) => handleCellChange(taskIndex, subtaskIndex, 'ANDROID', 'simple', e.target.value)}
                        />
                      </td>
                      <td className="px-3 py-2">
                        <input
                          type="number"
                          min="0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          value={getCellValue(taskIndex, subtaskIndex, 'ANDROID', 'medium')}
                          onChange={(e) => handleCellChange(taskIndex, subtaskIndex, 'ANDROID', 'medium', e.target.value)}
                        />
                      </td>
                      <td className="px-3 py-2">
                        <input
                          type="number"
                          min="0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          value={getCellValue(taskIndex, subtaskIndex, 'ANDROID', 'complex')}
                          onChange={(e) => handleCellChange(taskIndex, subtaskIndex, 'ANDROID', 'complex', e.target.value)}
                        />
                      </td>

                      {/* iOS columns */}
                      <td className="px-3 py-2 border-l border-gray-200">
                        <input
                          type="number"
                          min="0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          value={getCellValue(taskIndex, subtaskIndex, 'IOS', 'simple')}
                          onChange={(e) => handleCellChange(taskIndex, subtaskIndex, 'IOS', 'simple', e.target.value)}
                        />
                      </td>
                      <td className="px-3 py-2">
                        <input
                          type="number"
                          min="0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          value={getCellValue(taskIndex, subtaskIndex, 'IOS', 'medium')}
                          onChange={(e) => handleCellChange(taskIndex, subtaskIndex, 'IOS', 'medium', e.target.value)}
                        />
                      </td>
                      <td className="px-3 py-2">
                        <input
                          type="number"
                          min="0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          value={getCellValue(taskIndex, subtaskIndex, 'IOS', 'complex')}
                          onChange={(e) => handleCellChange(taskIndex, subtaskIndex, 'IOS', 'complex', e.target.value)}
                        />
                      </td>

                      {/* Hybrid App columns */}
                      <td className="px-3 py-2 border-l border-gray-200">
                        <input
                          type="number"
                          min="0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          value={getCellValue(taskIndex, subtaskIndex, 'HYBRID', 'simple')}
                          onChange={(e) => handleCellChange(taskIndex, subtaskIndex, 'HYBRID', 'simple', e.target.value)}
                        />
                      </td>
                      <td className="px-3 py-2">
                        <input
                          type="number"
                          min="0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          value={getCellValue(taskIndex, subtaskIndex, 'HYBRID', 'medium')}
                          onChange={(e) => handleCellChange(taskIndex, subtaskIndex, 'HYBRID', 'medium', e.target.value)}
                        />
                      </td>
                      <td className="px-3 py-2">
                        <input
                          type="number"
                          min="0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          value={getCellValue(taskIndex, subtaskIndex, 'HYBRID', 'complex')}
                          onChange={(e) => handleCellChange(taskIndex, subtaskIndex, 'HYBRID', 'complex', e.target.value)}
                        />
                      </td>
                    </tr>
                  ))}
                </React.Fragment>
              ))}
              
              {/* Totals row */}
              <tr className="bg-yellow-50 font-medium">
                <td className="px-6 py-3 text-sm font-bold">TOTALS</td>
                <td className="px-6 py-3"></td>
                <td className="px-3 py-3 text-sm font-bold border-l border-gray-200">{getWorkTypeTotal('UX', 'simple')}</td>
                <td className="px-3 py-3 text-sm font-bold">{getWorkTypeTotal('UX', 'medium')}</td>
                <td className="px-3 py-3 text-sm font-bold">{getWorkTypeTotal('UX', 'complex')}</td>
                <td className="px-3 py-3 text-sm font-bold border-l border-gray-200">{getWorkTypeTotal('FRONTEND', 'simple')}</td>
                <td className="px-3 py-3 text-sm font-bold">{getWorkTypeTotal('FRONTEND', 'medium')}</td>
                <td className="px-3 py-3 text-sm font-bold">{getWorkTypeTotal('FRONTEND', 'complex')}</td>
                <td className="px-3 py-3 text-sm font-bold border-l border-gray-200">{getWorkTypeTotal('QA', 'simple')}</td>
                <td className="px-3 py-3 text-sm font-bold">{getWorkTypeTotal('QA', 'medium')}</td>
                <td className="px-3 py-3 text-sm font-bold">{getWorkTypeTotal('QA', 'complex')}</td>
                <td className="px-3 py-3 text-sm font-bold border-l border-gray-200">{getWorkTypeTotal('API', 'simple')}</td>
                <td className="px-3 py-3 text-sm font-bold">{getWorkTypeTotal('API', 'medium')}</td>
                <td className="px-3 py-3 text-sm font-bold">{getWorkTypeTotal('API', 'complex')}</td>
                <td className="px-3 py-3 text-sm font-bold border-l border-gray-200">{getWorkTypeTotal('POWERAPPS', 'simple')}</td>
                <td className="px-3 py-3 text-sm font-bold">{getWorkTypeTotal('POWERAPPS', 'medium')}</td>
                <td className="px-3 py-3 text-sm font-bold">{getWorkTypeTotal('POWERAPPS', 'complex')}</td>
                <td className="px-3 py-3 text-sm font-bold border-l border-gray-200">{getWorkTypeTotal('ANDROID', 'simple')}</td>
                <td className="px-3 py-3 text-sm font-bold">{getWorkTypeTotal('ANDROID', 'medium')}</td>
                <td className="px-3 py-3 text-sm font-bold">{getWorkTypeTotal('ANDROID', 'complex')}</td>
                <td className="px-3 py-3 text-sm font-bold border-l border-gray-200">{getWorkTypeTotal('IOS', 'simple')}</td>
                <td className="px-3 py-3 text-sm font-bold">{getWorkTypeTotal('IOS', 'medium')}</td>
                <td className="px-3 py-3 text-sm font-bold">{getWorkTypeTotal('IOS', 'complex')}</td>
                <td className="px-3 py-3 text-sm font-bold border-l border-gray-200">{getWorkTypeTotal('HYBRID', 'simple')}</td>
                <td className="px-3 py-3 text-sm font-bold">{getWorkTypeTotal('HYBRID', 'medium')}</td>
                <td className="px-3 py-3 text-sm font-bold">{getWorkTypeTotal('HYBRID', 'complex')}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* Summary Section */}
      {summary && (
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Project Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-sm font-medium text-blue-600">Total Development Hours</div>
              <div className="text-2xl font-bold text-blue-900">{Math.round(summary.baseDevelopmentHours)}</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-sm font-medium text-green-600">Total SDLC Hours</div>
              <div className="text-2xl font-bold text-green-900">{Math.round(summary.totalSDLCHours)}</div>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="text-sm font-medium text-purple-600">Grand Total Hours</div>
              <div className="text-2xl font-bold text-purple-900">{Math.round(summary.grandTotalHours)}</div>
            </div>
            <div className="bg-orange-50 p-4 rounded-lg">
              <div className="text-sm font-medium text-orange-600">Total Weeks</div>
              <div className="text-2xl font-bold text-orange-900">{Math.round(summary.grandTotalWeeks * 10) / 10}</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AnalysisTable;
