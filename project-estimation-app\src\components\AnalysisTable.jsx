import React, { useState, useEffect } from 'react';
import { useProjects } from '../context/ProjectContext';
import { calculateProjectSummary, getWorkTypesForComponents } from '../utils/calculations';

const AnalysisTable = ({ project, onBack }) => {
  const { updateProject } = useProjects();
  const [analysisData, setAnalysisData] = useState(project.analysisData || {});
  const [summary, setSummary] = useState(null);
  const [tasks, setTasks] = useState(project.tasks || []);
  const [newTask, setNewTask] = useState({ category: '', subtask: '' });
  const [showAddTask, setShowAddTask] = useState(false);

  // Get work types based on selected components
  const workTypes = getWorkTypesForComponents(project.selectedComponents || []);

  useEffect(() => {
    const newSummary = calculateProjectSummary(analysisData);
    setSummary(newSummary);

    // Update project with new analysis data, summary, and tasks
    updateProject(project.id, {
      analysisData,
      summary: newSummary,
      tasks
    });
  }, [analysisData, tasks, project.id, updateProject]);

  const handleCellChange = (taskIndex, subtaskIndex, workType, complexity, value) => {
    const numValue = parseInt(value) || 0;
    
    setAnalysisData(prev => {
      const newData = { ...prev };
      
      // Initialize work type if it doesn't exist
      if (!newData[workType]) {
        newData[workType] = { simple: 0, medium: 0, complex: 0 };
      }
      
      // Create a unique key for this cell
      const cellKey = `${taskIndex}-${subtaskIndex}`;
      if (!newData[workType][complexity + '_breakdown']) {
        newData[workType][complexity + '_breakdown'] = {};
      }
      
      // Store the old value to calculate difference
      const oldValue = newData[workType][complexity + '_breakdown'][cellKey] || 0;
      const difference = numValue - oldValue;
      
      // Update the breakdown
      newData[workType][complexity + '_breakdown'][cellKey] = numValue;
      
      // Update the total for this complexity
      newData[workType][complexity] = (newData[workType][complexity] || 0) + difference;
      
      return newData;
    });
  };

  const getCellValue = (taskIndex, subtaskIndex, workType, complexity) => {
    if (!analysisData[workType] || !analysisData[workType][complexity + '_breakdown']) {
      return '';
    }
    const cellKey = `${taskIndex}-${subtaskIndex}`;
    return analysisData[workType][complexity + '_breakdown'][cellKey] || '';
  };

  const getWorkTypeTotal = (workType, complexity) => {
    return analysisData[workType]?.[complexity] || 0;
  };

  const addTask = () => {
    if (!newTask.category.trim() || !newTask.subtask.trim()) {
      alert('Please enter both category and subtask');
      return;
    }

    const existingCategoryIndex = tasks.findIndex(task => task.category === newTask.category);

    if (existingCategoryIndex >= 0) {
      // Add subtask to existing category
      const updatedTasks = [...tasks];
      updatedTasks[existingCategoryIndex].subtasks.push(newTask.subtask);
      setTasks(updatedTasks);
    } else {
      // Create new category
      setTasks(prev => [...prev, {
        category: newTask.category,
        subtasks: [newTask.subtask]
      }]);
    }

    setNewTask({ category: '', subtask: '' });
    setShowAddTask(false);
  };

  const removeTask = (taskIndex, subtaskIndex) => {
    const updatedTasks = [...tasks];
    updatedTasks[taskIndex].subtasks.splice(subtaskIndex, 1);

    // Remove category if no subtasks left
    if (updatedTasks[taskIndex].subtasks.length === 0) {
      updatedTasks.splice(taskIndex, 1);
    }

    setTasks(updatedTasks);
  };

  const getWorkTypeLabel = (workType) => {
    const labels = {
      'UX': 'UX',
      'FRONTEND': 'Frontend',
      'QA': 'QA',
      'API': 'API',
      'POWERAPPS': 'PowerApps',
      'ANDROID': 'Android',
      'IOS': 'iOS',
      'HYBRID': 'Hybrid'
    };
    return labels[workType] || workType;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <button
          onClick={onBack}
          className="text-blue-600 hover:text-blue-800 font-medium"
        >
          ← Back to Projects
        </button>
        <h2 className="text-2xl font-bold text-gray-900">{project.name} - Analysis</h2>
        <div></div>
      </div>

      <div className="bg-white shadow rounded-lg overflow-hidden">
        {/* Add Task Section */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <div className="flex justify-between items-center">
            <h4 className="text-md font-medium text-gray-900">Analysis Table</h4>
            <button
              onClick={() => setShowAddTask(!showAddTask)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium"
            >
              {showAddTask ? 'Cancel' : 'Add Task'}
            </button>
          </div>

          {showAddTask && (
            <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-3">
              <input
                type="text"
                placeholder="Category (e.g., Authentication)"
                value={newTask.category}
                onChange={(e) => setNewTask(prev => ({ ...prev, category: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
              />
              <input
                type="text"
                placeholder="Subtask (e.g., Login Page)"
                value={newTask.subtask}
                onChange={(e) => setNewTask(prev => ({ ...prev, subtask: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
              />
              <button
                onClick={addTask}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm font-medium"
              >
                Add Task
              </button>
            </div>
          )}
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Effort
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Subtasks
                </th>
                <th className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
                {workTypes.map((workType, index) => (
                  <th key={workType} colSpan="3" className={`px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider ${index === 0 ? 'border-l border-gray-200' : 'border-l border-gray-200'}`}>
                    {getWorkTypeLabel(workType)}
                  </th>
                ))}
              </tr>
              <tr className="bg-gray-100">
                <th className="px-6 py-2"></th>
                <th className="px-6 py-2 text-xs font-medium text-gray-500">Subtasks</th>
                <th className="px-3 py-2 text-xs font-medium text-gray-500">Actions</th>
                {workTypes.map((workType) => (
                  <React.Fragment key={workType}>
                    <th className="px-3 py-2 text-xs font-medium text-gray-500 border-l border-gray-200">Simple</th>
                    <th className="px-3 py-2 text-xs font-medium text-gray-500">Medium</th>
                    <th className="px-3 py-2 text-xs font-medium text-gray-500">Complex</th>
                  </React.Fragment>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {tasks.map((task, taskIndex) => (
                <React.Fragment key={taskIndex}>
                  <tr className="bg-blue-50">
                    <td colSpan={3 + (workTypes.length * 3)} className="px-6 py-3 text-sm font-medium text-gray-900">
                      {task.category}
                    </td>
                  </tr>
                  {task.subtasks.map((subtask, subtaskIndex) => (
                    <tr key={`${taskIndex}-${subtaskIndex}`} className="hover:bg-gray-50">
                      <td className="px-6 py-2"></td>
                      <td className="px-6 py-2 text-sm text-gray-900">{subtask}</td>
                      <td className="px-3 py-2 text-center">
                        <button
                          onClick={() => removeTask(taskIndex, subtaskIndex)}
                          className="text-red-600 hover:text-red-800 text-xs"
                          title="Remove task"
                        >
                          ✕
                        </button>
                      </td>

                      {/* Dynamic work type columns */}
                      {workTypes.map((workType, workTypeIndex) => (
                        <React.Fragment key={workType}>
                          <td className={`px-3 py-2 ${workTypeIndex === 0 ? 'border-l border-gray-200' : 'border-l border-gray-200'}`}>
                            <input
                              type="number"
                              min="0"
                              className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                              value={getCellValue(taskIndex, subtaskIndex, workType, 'simple')}
                              onChange={(e) => handleCellChange(taskIndex, subtaskIndex, workType, 'simple', e.target.value)}
                            />
                          </td>
                          <td className="px-3 py-2">
                            <input
                              type="number"
                              min="0"
                              className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                              value={getCellValue(taskIndex, subtaskIndex, workType, 'medium')}
                              onChange={(e) => handleCellChange(taskIndex, subtaskIndex, workType, 'medium', e.target.value)}
                            />
                          </td>
                          <td className="px-3 py-2">
                            <input
                              type="number"
                              min="0"
                              className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                              value={getCellValue(taskIndex, subtaskIndex, workType, 'complex')}
                              onChange={(e) => handleCellChange(taskIndex, subtaskIndex, workType, 'complex', e.target.value)}
                            />
                          </td>
                        </React.Fragment>
                      ))}
                    </tr>
                  ))}
                </React.Fragment>
              ))}
              
              {/* Totals row */}
              <tr className="bg-yellow-50 font-medium">
                <td className="px-6 py-3 text-sm font-bold">TOTALS</td>
                <td className="px-6 py-3"></td>
                <td className="px-3 py-3"></td>
                {workTypes.map((workType, workTypeIndex) => (
                  <React.Fragment key={workType}>
                    <td className={`px-3 py-3 text-sm font-bold ${workTypeIndex === 0 ? 'border-l border-gray-200' : 'border-l border-gray-200'}`}>
                      {getWorkTypeTotal(workType, 'simple')}
                    </td>
                    <td className="px-3 py-3 text-sm font-bold">
                      {getWorkTypeTotal(workType, 'medium')}
                    </td>
                    <td className="px-3 py-3 text-sm font-bold">
                      {getWorkTypeTotal(workType, 'complex')}
                    </td>
                  </React.Fragment>
                ))}
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* Summary Section */}
      {summary && (
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Project Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-sm font-medium text-blue-600">Total Development Hours</div>
              <div className="text-2xl font-bold text-blue-900">{Math.round(summary.baseDevelopmentHours)}</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-sm font-medium text-green-600">Total SDLC Hours</div>
              <div className="text-2xl font-bold text-green-900">{Math.round(summary.totalSDLCHours)}</div>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="text-sm font-medium text-purple-600">Grand Total Hours</div>
              <div className="text-2xl font-bold text-purple-900">{Math.round(summary.grandTotalHours)}</div>
            </div>
            <div className="bg-orange-50 p-4 rounded-lg">
              <div className="text-sm font-medium text-orange-600">Total Weeks</div>
              <div className="text-2xl font-bold text-orange-900">{Math.round(summary.grandTotalWeeks * 10) / 10}</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AnalysisTable;
