# Project Estimation Tool

A modern web application for project estimation that replicates Excel-based estimation workflows in a centralized portal.

## Features

- **Dynamic Component Selection**: Choose from UI/UX, UI, API, QA, Mobile, PowerApps, DevOps components
- **Completely Empty Start**: Analysis table starts with no default rows - fully customizable
- **Custom Task Creation**: Add your own categories and subtasks specific to your project
- **Smart Column Display**: Only shows work type columns for selected components
- **Task Management**: Remove tasks you don't need with simple ✕ button
- **Real-time Calculations**: Excel formulas work automatically as you add data
- **Conditional Summary**: Summary only appears when you have tasks and estimates
- **Complete SDLC Breakdown**: All multipliers, adjustments, and pricing calculations
- **Data Persistence**: Projects save automatically with all customizations

## Technology Stack

- **Frontend**: React 19 with Vite
- **Styling**: Tailwind CSS
- **State Management**: React Context
- **Data Storage**: Local Storage (can be upgraded to database)

## Installation

1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm run dev
```

3. Open your browser and navigate to `http://localhost:5173`

## Usage

1. **Create New Project**: Click "New Project Estimation"
2. **Select Components**: Choose relevant components (UI/UX, API, QA, Mobile, etc.)
3. **Fill Project Details**: Enter project name, client, description, and status
4. **Empty Analysis Table**: Table starts completely empty with only your selected work type columns
5. **Add Your First Task**: Click "Add Your First Task" or "Add Task" to create categories and subtasks
6. **Build Your Task List**: Add all the tasks specific to your project
7. **Enter Estimates**: Fill in effort estimates for Simple/Medium/Complex levels
8. **Remove Unwanted Tasks**: Use ✕ button to delete tasks you don't need
9. **Real-time Summary**: Watch calculations appear as you add estimates
10. **Manage Projects**: Edit, delete, or create new estimations as needed

## Project Structure

```
src/
├── components/
│   ├── Layout.jsx          # Main layout component
│   ├── ProjectList.jsx     # List of all projects
│   ├── NewProject.jsx      # Create new project form
│   ├── AnalysisTable.jsx   # Main estimation table
│   └── SummaryTable.jsx    # Detailed summary calculations
├── context/
│   └── ProjectContext.jsx  # State management
├── utils/
│   └── calculations.js     # Calculation utilities
└── App.jsx                 # Main application component
```

## Component Types

### Available Components:
- **UI/UX**: User Interface and User Experience Design (9/18/27 hours)
- **UI**: Frontend User Interface Development (9/27/45 hours)
- **API**: Backend API Development (18/45/72 hours)
- **QA**: Quality Assurance and Testing (9/27/45 hours)
- **Mobile**: Mobile App Development - Android/iOS/Hybrid (18/45/72 hours)
- **PowerApps**: Microsoft PowerApps Development (9/27/45 hours)
- **DevOps**: DevOps and Infrastructure (affects multipliers)

### Default Task Templates:
Each component comes with pre-defined task categories and subtasks that you can customize:
- **UI/UX**: User Research, Design System, Wireframing, UI Design
- **UI**: Frontend Setup, Component Development, State Management, Responsive Design
- **API**: API Design, Authentication, Core APIs, Integration
- **QA**: Test Planning, Manual Testing, Automation, Performance Testing
- **Mobile**: Mobile Setup, Core Features, Platform Specific, App Store
- **PowerApps**: PowerApps Setup, App Development, Integration, Deployment
- **DevOps**: Infrastructure, CI/CD Pipeline, Monitoring, Maintenance

## Calculation Logic

The application replicates Excel formulas for:
- Dynamic work type hours based on selected components
- SDLC multipliers (Infrastructure, Integration, Architecture, etc.)
- Contingency buffer (5% default)
- Project type adjustments
- Total effort in hours and weeks (45 hours per week)
