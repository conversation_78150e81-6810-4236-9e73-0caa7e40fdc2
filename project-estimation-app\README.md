# Project Estimation Tool

A modern web application for project estimation that replicates Excel-based estimation workflows in a centralized portal.

## Features

- **Dynamic Component Selection**: Choose from UI/UX, UI, API, QA, Mobile, PowerApps, DevOps components
- **Completely Empty Start**: Analysis table starts with no default rows - fully customizable
- **Custom Task Creation**: Add your own categories and subtasks specific to your project
- **Smart Column Display**: Only shows work type columns for selected components
- **Task Management**: Remove tasks you don't need with simple ✕ button
- **Configurable SDLC Multipliers**: Adjust Infrastructure, FE Integration, Architecture, QA Support, Manager Effort, and Contingency Buffer percentages
- **Real-time Calculations**: Excel formulas work automatically as you add data and adjust multipliers
- **Conditional Summary**: Summary only appears when you have tasks and estimates
- **Complete SDLC Breakdown**: All multipliers, adjustments, and pricing calculations
- **Data Persistence**: Projects save automatically with all customizations

## Technology Stack

- **Frontend**: React 19 with Vite
- **Styling**: Tailwind CSS
- **State Management**: React Context
- **Data Storage**: Local Storage (can be upgraded to database)

## Installation

1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm run dev
```

3. Open your browser and navigate to `http://localhost:5173`

## Usage

### Phase 1: Project Setup & Analysis
1. **Create New Project**: Click "New Project Estimation"
2. **Select Components**: Choose relevant components (UI/UX, API, QA, Mobile, etc.)
3. **Fill Project Details**: Enter project name, client, description, and status
4. **Empty Analysis Table**: Table starts completely empty with only your selected work type columns
5. **Add Your Tasks**: Click "Add Your First Task" to create categories and subtasks
6. **Build Complete Task List**: Add all tasks specific to your project
7. **Enter Effort Estimates**: Fill in Simple/Medium/Complex estimates for each task

### Phase 2: SDLC Configuration & Final Validation
8. **Review Base Development Summary**: See total hours from your analysis
9. **Configure SDLC Multipliers**: Adjust percentages for Infrastructure, FE Integration, Architecture, QA Support, Manager Effort, and Contingency Buffer based on project complexity
10. **Validate Final Summary**: Review the complete project estimate with all multipliers applied
11. **Manage Projects**: Save, edit, or create new estimations as needed

## Project Structure

```
src/
├── components/
│   ├── Layout.jsx          # Main layout component
│   ├── ProjectList.jsx     # List of all projects
│   ├── NewProject.jsx      # Create new project form
│   ├── AnalysisTable.jsx   # Main estimation table
│   └── SummaryTable.jsx    # Detailed summary calculations
├── context/
│   └── ProjectContext.jsx  # State management
├── utils/
│   └── calculations.js     # Calculation utilities
└── App.jsx                 # Main application component
```

## Component Types

### Available Components:
- **UI/UX**: User Interface and User Experience Design (9/18/27 hours)
- **UI**: Frontend User Interface Development (9/27/45 hours)
- **API**: Backend API Development (18/45/72 hours)
- **QA**: Quality Assurance and Testing (9/27/45 hours)
- **Mobile**: Mobile App Development - Android/iOS/Hybrid (18/45/72 hours)
- **PowerApps**: Microsoft PowerApps Development (9/27/45 hours)
- **DevOps**: DevOps and Infrastructure (affects multipliers)

### Default Task Templates:
Each component comes with pre-defined task categories and subtasks that you can customize:
- **UI/UX**: User Research, Design System, Wireframing, UI Design
- **UI**: Frontend Setup, Component Development, State Management, Responsive Design
- **API**: API Design, Authentication, Core APIs, Integration
- **QA**: Test Planning, Manual Testing, Automation, Performance Testing
- **Mobile**: Mobile Setup, Core Features, Platform Specific, App Store
- **PowerApps**: PowerApps Setup, App Development, Integration, Deployment
- **DevOps**: Infrastructure, CI/CD Pipeline, Monitoring, Maintenance

## SDLC Multipliers Configuration

The application includes configurable SDLC effort multipliers that you can adjust based on your project needs:

### Available Multipliers:
- **Infrastructure Support (Environment setup/CICD)**: 0% default
- **FE Integration**: 0% default
- **Front-End: Initial Architecture/Framework**: 0% default
- **API Integration**: 0% default
- **QA Additional Support**: 0% default
- **Manager Effort**: 0% default
- **Contingency Buffer**: 5% default

### How Multipliers Work:
- **Infrastructure, QA Support, Manager Effort, Contingency**: Applied to total development hours
- **FE Integration, Architecture**: Applied to frontend hours only
- **API Integration**: Applied to API hours only
- **All percentages are editable** and calculations update in real-time
- **Multipliers are saved** with each project for future reference

## Calculation Logic

The application replicates Excel formulas for:
- Dynamic work type hours based on selected components
- Configurable SDLC multipliers with real-time adjustment
- Project type adjustments
- Total effort in hours and weeks (45 hours per week)
