# Project Estimation Tool

A modern web application for project estimation that replicates Excel-based estimation workflows in a centralized portal.

## Features

- **Dynamic Component Selection**: Choose from UI/UX, UI, API, QA, Mobile, PowerApps, DevOps components
- **Smart Analysis Table**: Only shows columns for selected components
- **Dynamic Task Management**: Add/remove tasks and categories as needed
- **Real-time Calculations**: Excel formulas work automatically as you type
- **Intelligent Aggregation**: Summary shows only relevant work types
- **Complete SDLC Breakdown**: All multipliers, adjustments, and pricing calculations
- **Data Persistence**: Projects save automatically with all customizations

## Technology Stack

- **Frontend**: React 19 with Vite
- **Styling**: Tailwind CSS
- **State Management**: React Context
- **Data Storage**: Local Storage (can be upgraded to database)

## Installation

1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm run dev
```

3. Open your browser and navigate to `http://localhost:5173`

## Usage

1. **Create New Project**: Click "New Project Estimation"
2. **Select Components**: Choose relevant components (UI/UX, API, QA, Mobile, etc.)
3. **Fill Project Details**: Enter project name, client, description, and status
4. **Dynamic Analysis**: Table shows only columns for your selected components
5. **Add Custom Tasks**: Use "Add Task" to create project-specific tasks and categories
6. **Enter Estimates**: Fill in effort estimates for Simple/Medium/Complex levels
7. **Real-time Summary**: Watch calculations update automatically
8. **Manage Projects**: Edit, delete, or create new estimations as needed

## Project Structure

```
src/
├── components/
│   ├── Layout.jsx          # Main layout component
│   ├── ProjectList.jsx     # List of all projects
│   ├── NewProject.jsx      # Create new project form
│   ├── AnalysisTable.jsx   # Main estimation table
│   └── SummaryTable.jsx    # Detailed summary calculations
├── context/
│   └── ProjectContext.jsx  # State management
├── utils/
│   └── calculations.js     # Calculation utilities
└── App.jsx                 # Main application component
```

## Component Types

### Available Components:
- **UI/UX**: User Interface and User Experience Design (9/18/27 hours)
- **UI**: Frontend User Interface Development (9/27/45 hours)
- **API**: Backend API Development (18/45/72 hours)
- **QA**: Quality Assurance and Testing (9/27/45 hours)
- **Mobile**: Mobile App Development - Android/iOS/Hybrid (18/45/72 hours)
- **PowerApps**: Microsoft PowerApps Development (9/27/45 hours)
- **DevOps**: DevOps and Infrastructure (affects multipliers)

### Default Task Templates:
Each component comes with pre-defined task categories and subtasks that you can customize:
- **UI/UX**: User Research, Design System, Wireframing, UI Design
- **UI**: Frontend Setup, Component Development, State Management, Responsive Design
- **API**: API Design, Authentication, Core APIs, Integration
- **QA**: Test Planning, Manual Testing, Automation, Performance Testing
- **Mobile**: Mobile Setup, Core Features, Platform Specific, App Store
- **PowerApps**: PowerApps Setup, App Development, Integration, Deployment
- **DevOps**: Infrastructure, CI/CD Pipeline, Monitoring, Maintenance

## Calculation Logic

The application replicates Excel formulas for:
- Dynamic work type hours based on selected components
- SDLC multipliers (Infrastructure, Integration, Architecture, etc.)
- Contingency buffer (5% default)
- Project type adjustments
- Total effort in hours and weeks (45 hours per week)
