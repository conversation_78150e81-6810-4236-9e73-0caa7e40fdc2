# Project Estimation Tool

A modern web application for project estimation that replicates Excel-based estimation workflows in a centralized portal.

## Features

- **Project Management**: Create, edit, and manage multiple project estimations
- **Analysis Table**: Interactive table for effort estimation with editable fields
- **Automatic Calculations**: Real-time formula calculations for effort summary
- **Work Type Breakdown**: Support for UX, Frontend, QA, API, PowerApps, Android, and iOS
- **Summary Dashboard**: Detailed SDLC effort breakdown with multipliers
- **Data Persistence**: Local storage for saving project data

## Technology Stack

- **Frontend**: React 19 with Vite
- **Styling**: Tailwind CSS
- **State Management**: React Context
- **Data Storage**: Local Storage (can be upgraded to database)

## Installation

1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm run dev
```

3. Open your browser and navigate to `http://localhost:5173`

## Usage

1. **Create New Project**: Click "New Project Estimation" to create a new project
2. **Fill Project Details**: Enter project name, client, description, and status
3. **Analysis Table**: Fill in effort estimates for different work types and complexity levels
4. **View Summary**: Automatic calculations show total hours, weeks, and detailed breakdowns
5. **Save & Manage**: Projects are automatically saved and can be edited later

## Project Structure

```
src/
├── components/
│   ├── Layout.jsx          # Main layout component
│   ├── ProjectList.jsx     # List of all projects
│   ├── NewProject.jsx      # Create new project form
│   ├── AnalysisTable.jsx   # Main estimation table
│   └── SummaryTable.jsx    # Detailed summary calculations
├── context/
│   └── ProjectContext.jsx  # State management
├── utils/
│   └── calculations.js     # Calculation utilities
└── App.jsx                 # Main application component
```

## Calculation Logic

The application replicates Excel formulas for:
- Work type hours (Simple: 9h, Medium: 18-45h, Complex: 27-72h depending on type)
- SDLC multipliers (Infrastructure, Integration, Architecture, etc.)
- Contingency buffer (5% default)
- Project type adjustments
- Total effort in hours and weeks (45 hours per week)
