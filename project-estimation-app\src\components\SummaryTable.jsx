import React from 'react';
import { WORK_TYPES, MULTIPLIERS, HOURS_PER_WEEK, PRICING_RATE, getWorkTypesForComponents } from '../utils/calculations';

const SummaryTable = ({ summary, analysisData, selectedComponents = [], multipliers = {} }) => {
  if (!summary) return null;

  const workTypes = getWorkTypesForComponents(selectedComponents);

  // Use custom multipliers if provided, otherwise use defaults
  const activeMultipliers = { ...MULTIPLIERS, ...multipliers };

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium text-gray-900">Aggregation & Summary</h3>

      {/* Aggregation Section - Matching Excel Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

        {/* Left Column - Work Type Aggregations */}
        <div className="space-y-6">
          {workTypes.map((workType) => {
            const config = WORK_TYPES[workType];
            const data = analysisData[workType] || { simple: 0, medium: 0, complex: 0 };
            const workTypeLabels = {
              'UX': 'UX/BA',
              'FRONTEND': 'Frontend',
              'QA': 'QA',
              'API': 'API',
              'POWERAPPS': 'PowerApps',
              'ANDROID': 'Android',
              'IOS': 'iOS',
              'HYBRID': 'Hybrid App'
            };

            return (
              <div key={workType} className="bg-white shadow rounded-lg overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h4 className="text-md font-medium text-gray-900">{workTypeLabels[workType]}</h4>
                </div>
                <div className="p-4">
                  <table className="w-full text-sm">
                    <tbody>
                      <tr>
                        <td className="py-1">Hours per unit</td>
                        <td className="py-1 text-center">{config.simple.hours}</td>
                        <td className="py-1 text-center">{config.medium.hours}</td>
                        <td className="py-1 text-center">{config.complex.hours}</td>
                      </tr>
                      <tr>
                        <td className="py-1">Number of units</td>
                        <td className="py-1 text-center">{data.simple}</td>
                        <td className="py-1 text-center">{data.medium}</td>
                        <td className="py-1 text-center">{data.complex}</td>
                      </tr>
                      <tr>
                        <td className="py-1">Total Development Hours</td>
                        <td className="py-1 text-center">{data.simple * config.simple.hours}</td>
                        <td className="py-1 text-center">{data.medium * config.medium.hours}</td>
                        <td className="py-1 text-center">{data.complex * config.complex.hours}</td>
                      </tr>
                      <tr className="border-t font-medium">
                        <td className="py-1">Total</td>
                        <td colSpan="3" className="py-1 text-center">
                          {summary.workTypeBreakdown[workType]?.hours || 0}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            );
          })}
        </div>

        {/* Right Column - Summary */}
        <div className="space-y-6">

          {/* Summary Section */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h4 className="text-md font-medium text-gray-900">Summary</h4>
            </div>
            <div className="p-4">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2">Work Type</th>
                    <th className="text-center py-2">Multiplication Factor</th>
                    <th className="text-center py-2">P Hours</th>
                    <th className="text-center py-2">P Wks</th>
                  </tr>
                </thead>
                <tbody>
                  {workTypes.map((workType) => {
                    const workTypeLabels = {
                      'UX': 'UX/BA',
                      'FRONTEND': 'Frontend',
                      'QA': 'QA',
                      'API': 'API',
                      'POWERAPPS': 'PowerApps',
                      'ANDROID': 'Android',
                      'IOS': 'iOS',
                      'HYBRID': 'Hybrid App'
                    };
                    const hours = summary.workTypeBreakdown[workType]?.hours || 0;

                    return (
                      <tr key={workType}>
                        <td className="py-1">{workTypeLabels[workType]}</td>
                        <td className="py-1 text-center">1</td>
                        <td className="py-1 text-center">{hours}</td>
                        <td className="py-1 text-center">{Math.round((hours / HOURS_PER_WEEK) * 10) / 10}</td>
                      </tr>
                    );
                  })}
                  <tr className="border-t font-medium bg-blue-50">
                    <td className="py-2">Total Development</td>
                    <td className="py-2 text-center">-</td>
                    <td className="py-2 text-center">{Math.round(summary.baseDevelopmentHours)}</td>
                    <td className="py-2 text-center">{Math.round((summary.baseDevelopmentHours / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* Additional Efforts */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h4 className="text-md font-medium text-gray-900">Additional Efforts</h4>
            </div>
            <div className="p-4">
              <table className="w-full text-sm">
                <tbody>
                  <tr>
                    <td className="py-1">Infrastructure support (Environment setup/CICD)</td>
                    <td className="py-1 text-center">{Math.round(activeMultipliers.infrastructureSupport * 100 * 10) / 10}%</td>
                    <td className="py-1 text-center">{Math.round(summary.infrastructureHours)}</td>
                    <td className="py-1 text-center">{Math.round((summary.infrastructureHours / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr>
                    <td className="py-1">FE Integration</td>
                    <td className="py-1 text-center">{Math.round(activeMultipliers.feIntegration * 100 * 10) / 10}%</td>
                    <td className="py-1 text-center">{Math.round(summary.feIntegrationHours)}</td>
                    <td className="py-1 text-center">{Math.round((summary.feIntegrationHours / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr>
                    <td className="py-1">Front-End: Initial Architecture/Framework</td>
                    <td className="py-1 text-center">{Math.round(activeMultipliers.frontEndArchitecture * 100 * 10) / 10}%</td>
                    <td className="py-1 text-center">{Math.round(summary.frontEndArchitectureHours)}</td>
                    <td className="py-1 text-center">{Math.round((summary.frontEndArchitectureHours / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr>
                    <td className="py-1">API Integration</td>
                    <td className="py-1 text-center">{Math.round(activeMultipliers.apiIntegration * 100 * 10) / 10}%</td>
                    <td className="py-1 text-center">{Math.round(summary.apiIntegrationHours)}</td>
                    <td className="py-1 text-center">{Math.round((summary.apiIntegrationHours / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr>
                    <td className="py-1">QA Additional Support</td>
                    <td className="py-1 text-center">{Math.round(activeMultipliers.qaAdditionalSupport * 100 * 10) / 10}%</td>
                    <td className="py-1 text-center">{Math.round(summary.qaAdditionalHours)}</td>
                    <td className="py-1 text-center">{Math.round((summary.qaAdditionalHours / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr>
                    <td className="py-1">Manager Effort</td>
                    <td className="py-1 text-center">{Math.round(activeMultipliers.managerEffort * 100 * 10) / 10}%</td>
                    <td className="py-1 text-center">{Math.round(summary.managerEffortHours)}</td>
                    <td className="py-1 text-center">{Math.round((summary.managerEffortHours / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr>
                    <td className="py-1">Contingency Buffer</td>
                    <td className="py-1 text-center">{Math.round(activeMultipliers.contingencyBuffer * 100 * 10) / 10}%</td>
                    <td className="py-1 text-center">{Math.round(summary.contingencyHours)}</td>
                    <td className="py-1 text-center">{Math.round((summary.contingencyHours / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr className="border-t font-medium bg-green-50">
                    <td className="py-2">Total SDLC (A)</td>
                    <td className="py-2 text-center">-</td>
                    <td className="py-2 text-center">{Math.round(summary.totalSDLCHours)}</td>
                    <td className="py-2 text-center">{Math.round((summary.totalSDLCHours / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr>
                    <td className="py-1">Project Type Adjustment (B)</td>
                    <td className="py-1 text-center">{Math.round(activeMultipliers.projectTypeAdjustment * 100 * 10) / 10}%</td>
                    <td className="py-1 text-center">{Math.round(summary.projectTypeAdjustmentHours)}</td>
                    <td className="py-1 text-center">{Math.round((summary.projectTypeAdjustmentHours / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr className="border-t font-bold bg-yellow-50">
                    <td className="py-2">GRAND TOTAL (A + B)</td>
                    <td className="py-2 text-center">-</td>
                    <td className="py-2 text-center">{Math.round(summary.grandTotalHours)}</td>
                    <td className="py-2 text-center">{Math.round(summary.grandTotalWeeks * 10) / 10}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* Pricing */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h4 className="text-md font-medium text-gray-900">Approximate Pricing</h4>
            </div>
            <div className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  ${Math.round(summary.approximatePricing).toLocaleString()}
                </div>
                <div className="text-sm text-gray-500 mt-1">
                  {Math.round(summary.grandTotalHours)} hours × ${PRICING_RATE}/hour
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  );
};

export default SummaryTable;
