import React from 'react';
import { WORK_TYPES, MULTIPLIERS, HOURS_PER_WEEK, PRICING_RATE } from '../utils/calculations';

const SummaryTable = ({ summary, analysisData }) => {
  if (!summary) return null;

  const workTypes = ['UX', 'FRONTEND', 'QA', 'API', 'POWERAPPS', 'ANDROID', 'IOS', 'HYBRID'];

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium text-gray-900">Aggregation & Summary</h3>

      {/* Aggregation Section - Matching Excel Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

        {/* Left Column - Work Type Aggregations */}
        <div className="space-y-6">

          {/* UX/BA Section */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h4 className="text-md font-medium text-gray-900">UX/BA</h4>
            </div>
            <div className="p-4">
              <table className="w-full text-sm">
                <tbody>
                  <tr>
                    <td className="py-1">Hours per unit</td>
                    <td className="py-1 text-center">9</td>
                    <td className="py-1 text-center">18</td>
                    <td className="py-1 text-center">27</td>
                  </tr>
                  <tr>
                    <td className="py-1">Number of units</td>
                    <td className="py-1 text-center">{analysisData.UX?.simple || 0}</td>
                    <td className="py-1 text-center">{analysisData.UX?.medium || 0}</td>
                    <td className="py-1 text-center">{analysisData.UX?.complex || 0}</td>
                  </tr>
                  <tr>
                    <td className="py-1">Total Development Hours</td>
                    <td className="py-1 text-center">{(analysisData.UX?.simple || 0) * 9}</td>
                    <td className="py-1 text-center">{(analysisData.UX?.medium || 0) * 18}</td>
                    <td className="py-1 text-center">{(analysisData.UX?.complex || 0) * 27}</td>
                  </tr>
                  <tr className="border-t font-medium">
                    <td className="py-1">Total</td>
                    <td colSpan="3" className="py-1 text-center">{summary.uxTotal}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* Front End Section */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h4 className="text-md font-medium text-gray-900">Front End</h4>
            </div>
            <div className="p-4">
              <table className="w-full text-sm">
                <tbody>
                  <tr>
                    <td className="py-1">Hours per unit</td>
                    <td className="py-1 text-center">9</td>
                    <td className="py-1 text-center">27</td>
                    <td className="py-1 text-center">45</td>
                  </tr>
                  <tr>
                    <td className="py-1">Number of units</td>
                    <td className="py-1 text-center">{analysisData.FRONTEND?.simple || 0}</td>
                    <td className="py-1 text-center">{analysisData.FRONTEND?.medium || 0}</td>
                    <td className="py-1 text-center">{analysisData.FRONTEND?.complex || 0}</td>
                  </tr>
                  <tr>
                    <td className="py-1">Total Development Hours</td>
                    <td className="py-1 text-center">{(analysisData.FRONTEND?.simple || 0) * 9}</td>
                    <td className="py-1 text-center">{(analysisData.FRONTEND?.medium || 0) * 27}</td>
                    <td className="py-1 text-center">{(analysisData.FRONTEND?.complex || 0) * 45}</td>
                  </tr>
                  <tr className="border-t font-medium">
                    <td className="py-1">Total</td>
                    <td colSpan="3" className="py-1 text-center">{summary.frontendTotal}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* QA Section */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h4 className="text-md font-medium text-gray-900">QA</h4>
            </div>
            <div className="p-4">
              <table className="w-full text-sm">
                <tbody>
                  <tr>
                    <td className="py-1">Hours per unit</td>
                    <td className="py-1 text-center">9</td>
                    <td className="py-1 text-center">27</td>
                    <td className="py-1 text-center">45</td>
                  </tr>
                  <tr>
                    <td className="py-1">Number of units</td>
                    <td className="py-1 text-center">{analysisData.QA?.simple || 0}</td>
                    <td className="py-1 text-center">{analysisData.QA?.medium || 0}</td>
                    <td className="py-1 text-center">{analysisData.QA?.complex || 0}</td>
                  </tr>
                  <tr>
                    <td className="py-1">Total Development Hours</td>
                    <td className="py-1 text-center">{(analysisData.QA?.simple || 0) * 9}</td>
                    <td className="py-1 text-center">{(analysisData.QA?.medium || 0) * 27}</td>
                    <td className="py-1 text-center">{(analysisData.QA?.complex || 0) * 45}</td>
                  </tr>
                  <tr className="border-t font-medium">
                    <td className="py-1">Total</td>
                    <td colSpan="3" className="py-1 text-center">{summary.qaTotal}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* API Section */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h4 className="text-md font-medium text-gray-900">API</h4>
            </div>
            <div className="p-4">
              <table className="w-full text-sm">
                <tbody>
                  <tr>
                    <td className="py-1">Hours per unit</td>
                    <td className="py-1 text-center">18</td>
                    <td className="py-1 text-center">45</td>
                    <td className="py-1 text-center">72</td>
                  </tr>
                  <tr>
                    <td className="py-1">Number of units</td>
                    <td className="py-1 text-center">{analysisData.API?.simple || 0}</td>
                    <td className="py-1 text-center">{analysisData.API?.medium || 0}</td>
                    <td className="py-1 text-center">{analysisData.API?.complex || 0}</td>
                  </tr>
                  <tr>
                    <td className="py-1">Total Development Hours</td>
                    <td className="py-1 text-center">{(analysisData.API?.simple || 0) * 18}</td>
                    <td className="py-1 text-center">{(analysisData.API?.medium || 0) * 45}</td>
                    <td className="py-1 text-center">{(analysisData.API?.complex || 0) * 72}</td>
                  </tr>
                  <tr className="border-t font-medium">
                    <td className="py-1">Total</td>
                    <td colSpan="3" className="py-1 text-center">{summary.apiTotal}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Right Column - Summary */}
        <div className="space-y-6">

          {/* Summary Section */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h4 className="text-md font-medium text-gray-900">Summary</h4>
            </div>
            <div className="p-4">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2">Work Type</th>
                    <th className="text-center py-2">Multiplication Factor</th>
                    <th className="text-center py-2">P Hours</th>
                    <th className="text-center py-2">P Wks</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td className="py-1">UX/BA</td>
                    <td className="py-1 text-center">1</td>
                    <td className="py-1 text-center">{summary.uxTotal}</td>
                    <td className="py-1 text-center">{Math.round((summary.uxTotal / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr>
                    <td className="py-1">Front End</td>
                    <td className="py-1 text-center">1</td>
                    <td className="py-1 text-center">{summary.frontendTotal}</td>
                    <td className="py-1 text-center">{Math.round((summary.frontendTotal / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr>
                    <td className="py-1">QA</td>
                    <td className="py-1 text-center">1</td>
                    <td className="py-1 text-center">{summary.qaTotal}</td>
                    <td className="py-1 text-center">{Math.round((summary.qaTotal / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr>
                    <td className="py-1">API</td>
                    <td className="py-1 text-center">1</td>
                    <td className="py-1 text-center">{summary.apiTotal}</td>
                    <td className="py-1 text-center">{Math.round((summary.apiTotal / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr>
                    <td className="py-1">PowerApps</td>
                    <td className="py-1 text-center">1</td>
                    <td className="py-1 text-center">{summary.powerAppsTotal}</td>
                    <td className="py-1 text-center">{Math.round((summary.powerAppsTotal / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr>
                    <td className="py-1">Android</td>
                    <td className="py-1 text-center">1</td>
                    <td className="py-1 text-center">{summary.androidTotal}</td>
                    <td className="py-1 text-center">{Math.round((summary.androidTotal / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr>
                    <td className="py-1">iOS</td>
                    <td className="py-1 text-center">1</td>
                    <td className="py-1 text-center">{summary.iosTotal}</td>
                    <td className="py-1 text-center">{Math.round((summary.iosTotal / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr>
                    <td className="py-1">Hybrid App</td>
                    <td className="py-1 text-center">1</td>
                    <td className="py-1 text-center">{summary.hybridTotal}</td>
                    <td className="py-1 text-center">{Math.round((summary.hybridTotal / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr className="border-t font-medium bg-blue-50">
                    <td className="py-2">Total Development</td>
                    <td className="py-2 text-center">-</td>
                    <td className="py-2 text-center">{Math.round(summary.baseDevelopmentHours)}</td>
                    <td className="py-2 text-center">{Math.round((summary.baseDevelopmentHours / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* Additional Efforts */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h4 className="text-md font-medium text-gray-900">Additional Efforts</h4>
            </div>
            <div className="p-4">
              <table className="w-full text-sm">
                <tbody>
                  <tr>
                    <td className="py-1">Infrastructure support (Environment setup/CICD)</td>
                    <td className="py-1 text-center">{MULTIPLIERS.infrastructureSupport}</td>
                    <td className="py-1 text-center">{Math.round(summary.infrastructureHours)}</td>
                    <td className="py-1 text-center">{Math.round((summary.infrastructureHours / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr>
                    <td className="py-1">FE Integration</td>
                    <td className="py-1 text-center">{MULTIPLIERS.feIntegration}</td>
                    <td className="py-1 text-center">{Math.round(summary.feIntegrationHours)}</td>
                    <td className="py-1 text-center">{Math.round((summary.feIntegrationHours / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr>
                    <td className="py-1">Front-End: Initial Architecture/Framework</td>
                    <td className="py-1 text-center">{MULTIPLIERS.frontEndArchitecture}</td>
                    <td className="py-1 text-center">{Math.round(summary.frontEndArchitectureHours)}</td>
                    <td className="py-1 text-center">{Math.round((summary.frontEndArchitectureHours / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr>
                    <td className="py-1">API Integration</td>
                    <td className="py-1 text-center">{MULTIPLIERS.apiIntegration}</td>
                    <td className="py-1 text-center">{Math.round(summary.apiIntegrationHours)}</td>
                    <td className="py-1 text-center">{Math.round((summary.apiIntegrationHours / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr>
                    <td className="py-1">QA Additional Support</td>
                    <td className="py-1 text-center">{MULTIPLIERS.qaAdditionalSupport}</td>
                    <td className="py-1 text-center">{Math.round(summary.qaAdditionalHours)}</td>
                    <td className="py-1 text-center">{Math.round((summary.qaAdditionalHours / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr>
                    <td className="py-1">Manager Effort</td>
                    <td className="py-1 text-center">{MULTIPLIERS.managerEffort}</td>
                    <td className="py-1 text-center">{Math.round(summary.managerEffortHours)}</td>
                    <td className="py-1 text-center">{Math.round((summary.managerEffortHours / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr>
                    <td className="py-1">Contingency Buffer</td>
                    <td className="py-1 text-center">{MULTIPLIERS.contingencyBuffer}</td>
                    <td className="py-1 text-center">{Math.round(summary.contingencyHours)}</td>
                    <td className="py-1 text-center">{Math.round((summary.contingencyHours / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr className="border-t font-medium bg-green-50">
                    <td className="py-2">Total SDLC (A)</td>
                    <td className="py-2 text-center">-</td>
                    <td className="py-2 text-center">{Math.round(summary.totalSDLCHours)}</td>
                    <td className="py-2 text-center">{Math.round((summary.totalSDLCHours / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr>
                    <td className="py-1">Project Type Adjustment (B)</td>
                    <td className="py-1 text-center">{MULTIPLIERS.projectTypeAdjustment}</td>
                    <td className="py-1 text-center">{Math.round(summary.projectTypeAdjustmentHours)}</td>
                    <td className="py-1 text-center">{Math.round((summary.projectTypeAdjustmentHours / HOURS_PER_WEEK) * 10) / 10}</td>
                  </tr>
                  <tr className="border-t font-bold bg-yellow-50">
                    <td className="py-2">GRAND TOTAL (A + B)</td>
                    <td className="py-2 text-center">-</td>
                    <td className="py-2 text-center">{Math.round(summary.grandTotalHours)}</td>
                    <td className="py-2 text-center">{Math.round(summary.grandTotalWeeks * 10) / 10}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* Pricing */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h4 className="text-md font-medium text-gray-900">Approximate Pricing</h4>
            </div>
            <div className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  ${Math.round(summary.approximatePricing).toLocaleString()}
                </div>
                <div className="text-sm text-gray-500 mt-1">
                  {Math.round(summary.grandTotalHours)} hours × ${PRICING_RATE}/hour
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  );
};

export default SummaryTable;
