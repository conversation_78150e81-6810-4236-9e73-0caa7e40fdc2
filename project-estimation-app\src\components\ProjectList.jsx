import React from 'react';
import { useProjects } from '../context/ProjectContext';

const ProjectList = ({ onNewProject, onEditProject }) => {
  const { projects, deleteProject } = useProjects();

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  const handleDelete = (id, projectName) => {
    if (window.confirm(`Are you sure you want to delete "${projectName}"?`)) {
      deleteProject(id);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Project Estimations</h2>
        <button
          onClick={onNewProject}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
        >
          New Project Estimation
        </button>
      </div>

      {projects.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-500 text-lg">No projects found</div>
          <p className="text-gray-400 mt-2">Create your first project estimation to get started</p>
          <button
            onClick={onNewProject}
            className="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium"
          >
            Create First Project
          </button>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {projects.map((project) => (
              <li key={project.id}>
                <div className="px-4 py-4 flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-lg font-medium text-blue-600 truncate">
                        {project.name}
                      </p>
                      <div className="ml-2 flex-shrink-0 flex">
                        <p className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          {project.status || 'Draft'}
                        </p>
                      </div>
                    </div>
                    <div className="mt-2 flex">
                      <div className="flex items-center text-sm text-gray-500">
                        <p>
                          Created: {formatDate(project.createdAt)}
                          {project.updatedAt !== project.createdAt && (
                            <span className="ml-2">
                              • Updated: {formatDate(project.updatedAt)}
                            </span>
                          )}
                        </p>
                      </div>
                    </div>
                    {project.description && (
                      <div className="mt-2">
                        <p className="text-sm text-gray-600">{project.description}</p>
                      </div>
                    )}
                    {project.summary && (
                      <div className="mt-2 flex space-x-4 text-sm text-gray-500">
                        <span>Total Hours: {Math.round(project.summary.grandTotalHours)}</span>
                        <span>Total Weeks: {Math.round(project.summary.grandTotalWeeks * 10) / 10}</span>
                      </div>
                    )}
                  </div>
                  <div className="ml-4 flex space-x-2">
                    <button
                      onClick={() => onEditProject(project)}
                      className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded text-sm font-medium"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDelete(project.id, project.name)}
                      className="bg-red-100 hover:bg-red-200 text-red-700 px-3 py-1 rounded text-sm font-medium"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default ProjectList;
