import React, { useState } from 'react';
import { ProjectProvider } from './context/ProjectContext';
import Layout from './components/Layout';
import ProjectList from './components/ProjectList';
import NewProject from './components/NewProject';
import AnalysisTable from './components/AnalysisTable';
import SummaryTable from './components/SummaryTable';

function App() {
  const [currentView, setCurrentView] = useState('list'); // 'list', 'new', 'edit', 'summary'
  const [selectedProject, setSelectedProject] = useState(null);

  const handleNewProject = () => {
    setCurrentView('new');
  };

  const handleEditProject = (project) => {
    setSelectedProject(project);
    setCurrentView('edit');
  };

  const handleProjectCreated = (project) => {
    setSelectedProject(project);
    setCurrentView('edit');
  };

  const handleBackToList = () => {
    setCurrentView('list');
    setSelectedProject(null);
  };

  const handleViewSummary = () => {
    setCurrentView('summary');
  };

  const renderContent = () => {
    switch (currentView) {
      case 'new':
        return (
          <NewProject
            onBack={handleBackToList}
            onProjectCreated={handleProjectCreated}
          />
        );
      case 'edit':
        return selectedProject ? (
          <div className="space-y-6">
            <AnalysisTable
              project={selectedProject}
              onBack={handleBackToList}
            />
            {selectedProject.summary && (
              <SummaryTable
                summary={selectedProject.summary}
                analysisData={selectedProject.analysisData}
                selectedComponents={selectedProject.selectedComponents}
              />
            )}
          </div>
        ) : null;
      case 'summary':
        return selectedProject ? (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <button
                onClick={() => setCurrentView('edit')}
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                ← Back to Analysis
              </button>
              <h2 className="text-2xl font-bold text-gray-900">{selectedProject.name} - Summary</h2>
              <div></div>
            </div>
            <SummaryTable
              summary={selectedProject.summary}
              analysisData={selectedProject.analysisData}
              selectedComponents={selectedProject.selectedComponents}
            />
          </div>
        ) : null;
      default:
        return (
          <ProjectList
            onNewProject={handleNewProject}
            onEditProject={handleEditProject}
          />
        );
    }
  };

  return (
    <ProjectProvider>
      <Layout>
        {renderContent()}
      </Layout>
    </ProjectProvider>
  );
}

export default App;
