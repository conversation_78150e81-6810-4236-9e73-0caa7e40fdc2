<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Estimation Tool - Component Test</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: system-ui, -apple-system, sans-serif; }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, createContext, useContext } = React;

        // Simple Project Context
        const ProjectContext = createContext();

        const ProjectProvider = ({ children }) => {
            const [projects, setProjects] = useState([]);
            
            const addProject = (project) => {
                const newProject = {
                    ...project,
                    id: Date.now().toString(),
                    createdAt: new Date().toISOString(),
                };
                setProjects(prev => [...prev, newProject]);
                return newProject;
            };

            return (
                <ProjectContext.Provider value={{ projects, addProject }}>
                    {children}
                </ProjectContext.Provider>
            );
        };

        const useProjects = () => useContext(ProjectContext);

        // Simple Layout Component
        const Layout = ({ children, title }) => (
            <div className="min-h-screen bg-gray-50">
                <header className="bg-white shadow-sm border-b">
                    <div className="max-w-7xl mx-auto px-4 py-6">
                        <h1 className="text-2xl font-bold text-gray-900">
                            Project Estimation Tool
                        </h1>
                    </div>
                </header>
                <main className="max-w-7xl mx-auto py-6 px-4">
                    {title && <h2 className="text-3xl font-bold text-gray-900 mb-6">{title}</h2>}
                    {children}
                </main>
            </div>
        );

        // Simple Project List Component
        const ProjectList = ({ onNewProject }) => {
            const { projects } = useProjects();

            return (
                <div className="space-y-6">
                    <div className="flex justify-between items-center">
                        <h2 className="text-2xl font-bold text-gray-900">Project Estimations</h2>
                        <button
                            onClick={onNewProject}
                            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
                        >
                            New Project Estimation
                        </button>
                    </div>

                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                        <h3 className="text-lg font-medium text-blue-900 mb-2">✅ Completely Dynamic Project Estimation Tool</h3>
                        <ul className="text-sm text-blue-800 space-y-1">
                            <li>• <strong>Component Selection:</strong> Choose UI/UX, UI, API, QA, Mobile, PowerApps, DevOps</li>
                            <li>• <strong>Empty Analysis Table:</strong> Starts completely empty - no default rows</li>
                            <li>• <strong>Add Your Own Tasks:</strong> Create custom categories and subtasks</li>
                            <li>• <strong>Dynamic Columns:</strong> Only shows work types for selected components</li>
                            <li>• <strong>Remove Tasks:</strong> Delete tasks you don't need with ✕ button</li>
                            <li>• <strong>Real-time Calculations:</strong> Excel formulas work as you add data</li>
                            <li>• <strong>Smart Summary:</strong> Only appears when you have tasks and estimates</li>
                        </ul>
                    </div>

                    {projects.length === 0 ? (
                        <div className="text-center py-12">
                            <div className="text-gray-500 text-lg">No projects found</div>
                            <p className="text-gray-400 mt-2">Create your first project estimation to get started</p>
                        </div>
                    ) : (
                        <div className="bg-white shadow rounded-lg p-6">
                            <h3 className="text-lg font-medium mb-4">Your Projects</h3>
                            {projects.map(project => (
                                <div key={project.id} className="border-b py-3 last:border-b-0">
                                    <h4 className="font-medium text-blue-600">{project.name}</h4>
                                    <p className="text-sm text-gray-500">Created: {new Date(project.createdAt).toLocaleDateString()}</p>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            );
        };

        // Simple New Project Form
        const NewProject = ({ onBack }) => {
            const { addProject } = useProjects();
            const [formData, setFormData] = useState({ name: '', description: '' });

            const handleSubmit = (e) => {
                e.preventDefault();
                if (formData.name.trim()) {
                    addProject(formData);
                    setFormData({ name: '', description: '' });
                    alert('Project created successfully!');
                }
            };

            return (
                <div className="max-w-2xl mx-auto">
                    <button
                        onClick={onBack}
                        className="text-blue-600 hover:text-blue-800 font-medium mb-6"
                    >
                        ← Back to Projects
                    </button>

                    <div className="bg-white shadow rounded-lg p-6">
                        <h3 className="text-lg font-medium mb-6">Create New Project Estimation</h3>
                        
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Project Name *
                                </label>
                                <input
                                    type="text"
                                    value={formData.name}
                                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="Enter project name"
                                    required
                                />
                            </div>
                            
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Description
                                </label>
                                <textarea
                                    value={formData.description}
                                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                    rows="3"
                                    placeholder="Enter project description"
                                />
                            </div>
                            
                            <div className="flex justify-end space-x-3 pt-4">
                                <button
                                    type="button"
                                    onClick={onBack}
                                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                                >
                                    Cancel
                                </button>
                                <button
                                    type="submit"
                                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                                >
                                    Create Project
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            );
        };

        // Main App Component
        const App = () => {
            const [currentView, setCurrentView] = useState('list');

            return (
                <ProjectProvider>
                    <Layout>
                        {currentView === 'list' ? (
                            <ProjectList onNewProject={() => setCurrentView('new')} />
                        ) : (
                            <NewProject onBack={() => setCurrentView('list')} />
                        )}
                    </Layout>
                </ProjectProvider>
            );
        };

        // Render the app
        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
